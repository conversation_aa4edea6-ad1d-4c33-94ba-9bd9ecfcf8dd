"use client";

import { getAnalytics, isSupported } from "firebase/analytics";
import { initializeApp } from "firebase/app";
import { getAuth } from "firebase/auth";
import { getFirestore } from "firebase/firestore";
import { getFunctions } from "firebase/functions";
import { getStorage } from "firebase/storage";
import { useRouter } from "next/navigation";
import type { ReactNode } from "react";
import { createContext, useContext, useEffect, useState } from "react";

import { getUserById, getUserRole } from "@/api/auth-api";
import { UserEntity } from "./core.constants";

const firebaseConfig = {
  apiKey: process.env.NEXT_PUBLIC_FIREBASE_API_KEY,
  authDomain: process.env.NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN,
  projectId: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID,
  storageBucket: process.env.NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET,
  messagingSenderId: process.env.NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID,
  appId: process.env.NEXT_PUBLIC_FIREBASE_APP_ID,
};

const app = initializeApp(firebaseConfig);
export const firestore = getFirestore(app);
export const firebaseStorage = getStorage(app);
export const firebaseAuth = getAuth();
export const firebaseFunctions = getFunctions(app);
export const analytics = isSupported().then((yes) =>
  yes ? getAnalytics(app) : null
);

interface RootContextType {
  resetUser: () => void;
  currentUser: UserEntity | null;
  role: string;
  setCurrentUser: (user: UserEntity | null) => void;
  setRole: (role: string) => void;
}

const RootContext = createContext<RootContextType | undefined>(undefined);

export const useRootContext = () => {
  const context = useContext(RootContext);
  if (context === undefined) {
    throw new Error("useRootContext must be used within a RootProvider");
  }
  return context;
};

export const RootProvider = ({ children }: { children: ReactNode }) => {
  const [role, setRole] = useState("");
  const [currentUser, setCurrentUser] = useState<UserEntity | null>(null);
  const router = useRouter();

  useEffect(() => {
    const unsubscribe = firebaseAuth.onAuthStateChanged((user) => {
      if (user) {
        getUserById(user.uid).then((user) => {
          setCurrentUser(user);
        });

        getUserRole().then((role) => {
          setRole(role);
          // Only redirect non-admin users if they're trying to access admin pages
          // Don't redirect them from other pages like profile
          if (
            role !== "admin" &&
            window.location.pathname.startsWith("/admin")
          ) {
            router.push("/");
          }
        });
      }
    });

    // Cleanup subscription on unmount
    return () => unsubscribe();
  }, [router]);

  const resetUser = () => {
    setRole("");
    setCurrentUser(null);
  };

  return (
    <RootContext.Provider
      value={{
        resetUser,
        currentUser,
        role,
        setCurrentUser,
        setRole,
      }}
    >
      {children}
    </RootContext.Provider>
  );
};
