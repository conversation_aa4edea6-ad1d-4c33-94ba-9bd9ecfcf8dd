import { Telegraf } from "telegraf";
import dotenv from "dotenv";
import { handleStartCommand, handleHelpCommand } from "./handlers/commands";
import {
  handleHelloWorldButton,
  handleGetMyOrdersButton,
  handleCompleteOrderButton,
  handleEchoGiftButton,
  handleGetReferralLinkButton,
} from "./handlers/buttons";
import {
  handleOrderHelpCallback,
  handleContactSupportCallback,
  handleOpenMarketplaceCallback,
  handleCancelEchoModeCallback,
  handleOrderSelectionCallback,
  handleBackToOrdersCallback,
  handleOrderCompletionCallback,
  handleBackToMenuCallback,
} from "./handlers/callbacks";
import { handleMessage } from "./handlers/messages";

// Load environment variables
dotenv.config();

const BOT_TOKEN = process.env.BOT_TOKEN;

if (!BOT_TOKEN) {
  throw new Error("BOT_TOKEN is required in environment variables");
}

// Create bot instance
const bot = new Telegraf(BOT_TOKEN);

// Command handlers
bot.start(handleStartCommand);
bot.help(handleHelpCommand);

// Button handlers
bot.hears("👋 Hello World", handleHelloWorldButton);
bot.hears("📋 Get My Orders", handleGetMyOrdersButton);
bot.hears("✅ Complete Order", handleCompleteOrderButton);
bot.hears("🎁 Echo Gift", handleEchoGiftButton);
bot.hears("🔗 Get Referral Link", handleGetReferralLinkButton);

// Callback handlers
bot.action("order_help", handleOrderHelpCallback);
bot.action("contact_support", handleContactSupportCallback);
bot.action("open_marketplace", handleOpenMarketplaceCallback);
bot.action("cancel_echo_mode", handleCancelEchoModeCallback);
bot.action(/^order_(.+)$/, handleOrderSelectionCallback);
bot.action("back_to_orders", handleBackToOrdersCallback);
bot.action(/^complete_(.+)$/, handleOrderCompletionCallback);
bot.action("back_to_menu", handleBackToMenuCallback);

// Message handler
bot.on("message", handleMessage);

// Error handling
bot.catch((err, ctx) => {
  console.error("Bot error:", err);
  ctx.reply("Sorry, something went wrong. Please try again later.");
});

export default bot;
