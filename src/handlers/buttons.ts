import { Context, Markup } from "telegraf";
import {
  getUserOrdersByTgId,
  formatOrderForDisplay,
  getCompletableOrders,
} from "../firebase-service";
import {
  createMarketplaceInlineKeyboard,
  createOrderHelpKeyboard,
  createEchoModeKeyboard,
  createReferralKeyboard,
  WEB_APP_URL,
} from "../utils/keyboards";
import { setUserSession } from "../services/session";

export const handleHelloWorldButton = (ctx: Context) => {
  ctx.reply(
    "👋 Hello World! Welcome to our marketplace bot.\n\n" +
      "This is a simple greeting message. You can use this bot to:\n" +
      "• Access the marketplace\n" +
      "• Complete orders\n" +
      "• Get support\n\n" +
      "Use the menu button above to open the full marketplace experience!",
    createMarketplaceInlineKeyboard()
  );
};

export const handleGetMyOrdersButton = async (ctx: Context) => {
  try {
    const tgId = ctx.from?.id?.toString();
    if (!tgId) {
      ctx.reply("❌ Unable to identify your Telegram ID. Please try again.");
      return;
    }

    ctx.reply("🔍 Fetching your orders...");

    const ordersResponse = await getUserOrdersByTgId(tgId);

    if (!ordersResponse.success || ordersResponse.orders.length === 0) {
      ctx.reply(
        "📭 You don't have any orders yet.\n\n" +
          "Create your first order using the marketplace web app!",
        createMarketplaceInlineKeyboard()
      );
      return;
    }

    const orders = ordersResponse.orders;
    const completableOrders = getCompletableOrders(orders);

    let message = `📋 Your Orders (${orders.length} total)\n\n`;

    if (completableOrders.length > 0) {
      message += `🟠 Orders ready for completion: ${completableOrders.length}\n\n`;
    }

    // Create inline keyboard with order buttons
    const orderButtons = orders
      .slice(0, 10)
      .map((order) => [
        Markup.button.callback(
          `${formatOrderForDisplay(order)}`,
          `order_${order.id}`
        ),
      ]);

    // Add navigation buttons
    orderButtons.push([
      Markup.button.callback("🌐 Open Marketplace", "open_marketplace"),
    ]);

    if (orders.length > 10) {
      message += `\n📝 Showing first 10 orders. Use the web app to see all orders.`;
    }

    ctx.reply(message, Markup.inlineKeyboard(orderButtons));
  } catch (error) {
    console.error("Error fetching user orders:", error);
    ctx.reply(
      "❌ Failed to fetch your orders. Please try again later.",
      createMarketplaceInlineKeyboard()
    );
  }
};

export const handleCompleteOrderButton = (ctx: Context) => {
  ctx.reply(
    "📦 Complete Order\n\n" +
      "To complete an order, please use the marketplace web app where you can:\n" +
      "• View your active orders\n" +
      "• Track order status\n" +
      "• Complete payment\n" +
      "• Leave feedback\n\n" +
      "Click the button below to open the marketplace:",
    createOrderHelpKeyboard()
  );
};

export const handleEchoGiftButton = (ctx: Context) => {
  const userId = ctx.from?.id?.toString();
  if (!userId) {
    ctx.reply("❌ Unable to identify your Telegram ID. Please try again.");
    return;
  }

  // Set echo mode for this user
  setUserSession(userId, { echoMode: true });

  ctx.reply(
    "🎁 Echo Gift Mode Activated!\n\n" +
      "Send me any gift (photo, document, text, etc.) and I'll echo it back to you with logged data.\n\n" +
      "📝 The gift data will be logged to the console for debugging purposes.\n\n" +
      "To exit echo mode, use /start or click Cancel below.",
    createEchoModeKeyboard()
  );
};

export const handleGetReferralLinkButton = (ctx: Context) => {
  const tgId = ctx.from?.id?.toString();
  if (!tgId) {
    ctx.reply("❌ Unable to identify your Telegram ID. Please try again.");
    return;
  }

  const referralLink = `${WEB_APP_URL}?referral_id=${tgId}`;

  ctx.reply(
    "🔗 Your Referral Link\n\n" +
      "Share this link with friends to earn referral rewards! When someone signs up using your link and makes a purchase, you'll receive a percentage of the marketplace fee.\n\n" +
      `📎 Your referral link:\n${referralLink}\n\n` +
      "💰 How it works:\n" +
      "• Share your link with friends\n" +
      "• They open the marketplace using your link\n" +
      "• When they make their first purchase, you earn a referral fee\n" +
      "• The fee is automatically added to your marketplace balance\n\n" +
      "🎯 Start earning by sharing your link now!",
    createReferralKeyboard(referralLink)
  );
};
