import { Context } from "telegraf";
import {
  createMainKeyboard,
  createMarketplaceInlineKeyboard,
} from "../utils/keyboards";

export const handleStartCommand = (ctx: Context) => {
  const welcomeMessage = `
🛍️ Welcome to the Marketplace Bot!

This bot helps you access our marketplace platform. Use the buttons below to get started or open the full marketplace using the menu button.
  `;

  ctx.reply(welcomeMessage, createMainKeyboard());
};

export const handleHelpCommand = (ctx: Context) => {
  ctx.reply(
    "🤖 Marketplace Bot Help\n\n" +
      "Available commands:\n" +
      "/start - Start the bot and show main menu\n" +
      "/help - Show this help message\n\n" +
      "Available buttons:\n" +
      "👋 Hello World - Get a welcome message\n" +
      "📋 Get My Orders - View and manage your orders\n" +
      "✅ Complete Order - Get help with completing orders\n" +
      "🎁 Echo Gift - Send a gift to the bot and get it echoed back\n\n" +
      "Order completion flow:\n" +
      "1. Use 'Get My Orders' to see orders ready for completion\n" +
      "2. Click on a paid order to start completion\n" +
      "3. Send the gift/item to this bot\n" +
      "4. The bot will automatically complete the purchase\n\n" +
      "You can also use the menu button to open the full marketplace web app.",
    createMarketplaceInlineKeyboard()
  );
};
